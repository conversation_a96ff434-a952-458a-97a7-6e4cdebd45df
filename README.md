# Financial trading using Technical and Timeseries Analysis. [![Open in Streamlit](https://static.streamlit.io/badges/streamlit_badge_black_white.svg)](https://share.streamlit.io/akurgat/automating-technical-analysis/Trade.py)

## Links:

* https://share.streamlit.io/akurgat/automating-technical-analysis/Trade.py

## Project goal:

Profitable stocks and crypto trading involves a lot of know how and experience in Technical Analysis. However, the fundamentals behind technical analysis techniques, tools, resources and effective strategies can be complex to grasp, understand and even expensive to access.

## Solution:

The main point of any sort of asset trading is to make a profit. This boils down to effectively three actions based on the price movements, **&#39;When should I buy?&#39;**, **&#39;When should I sell?&#39;** and **&#39;When should I hold my current position?&#39;** to maximize profits and minimize losses. Therefore, by using data analytics it was possible to translate real-time price movements to determine whether to buy, sell or hold based on historical price trends. This was achieved by combining a number of popularly used trading strategies and indicators such as **&#39;Moving Average Convergence Divergence&#39;**, **&#39;Slow Stochastic&#39;**, **&#39;Relative Strength Index&#39;** etc. More so, by feeding these sequences to a **Transformer Encoder Neural Network** to learn the price patterns and trading actions, the deep learning model could provide with the most appropriate action to be taken at any given time.
